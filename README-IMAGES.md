# 📸 دليل الصور المطلوبة

## 🔧 **أسماء الصور الجديدة:**

يجب إعادة تسمية الصور في مجلد `img/` كالتالي:

### **الصور الحالية ← الأسماء الجديدة:**

1. **`تصميم-موقع-الكتروني-.png`** ← `web-development.png`
2. **`0_lHeMJ6vBYiSYvPaZ.png`** ← `security-check.png`
3. **`image-31-780x439.png`** ← `bug-fixing.png`
4. **`60-Converted.png.webp`** ← `project-completion.png`
5. **`logo.png`** ← يبقى كما هو

---

## 🛠️ **خطوات الحل:**

### **الطريقة الأولى: إعادة تسمية الصور**
1. اذهب إلى مجلد `img/`
2. أعد تسمية كل صورة حسب الجدول أعلاه
3. ارفع الملفات مرة أخرى

### **الطريقة الثانية: استخدام صور بديلة**
إذا لم تجد الصور، يمكنك استخدام صور مجانية من:
- [Unsplash](https://unsplash.com)
- [Pexels](https://pexels.com)
- [Freepik](https://freepik.com)

---

## 📋 **مواصفات الصور المطلوبة:**

| الخدمة | اسم الملف | الحجم المقترح | الوصف |
|--------|-----------|---------------|-------|
| تطوير المواقع | `web-development.png` | 300x300px | أيقونة تطوير مواقع |
| فحص الثغرات | `security-check.png` | 300x300px | أيقونة أمان وحماية |
| إصلاح الأخطاء | `bug-fixing.png` | 300x300px | أيقونة إصلاح الأخطاء |
| إكمال المشاريع | `project-completion.png` | 300x300px | أيقونة إنجاز المشاريع |

---

## ⚠️ **ملاحظات مهمة:**

1. **تجنب الأحرف العربية** في أسماء الملفات
2. **تجنب المسافات** - استخدم `-` أو `_`
3. **استخدم أحرف صغيرة** فقط
4. **تأكد من امتداد الملف** (.png, .jpg, .webp)

---

## 🚀 **بعد التعديل:**

1. ارفع الملفات الجديدة
2. احذف الملفات القديمة
3. اختبر الموقع
