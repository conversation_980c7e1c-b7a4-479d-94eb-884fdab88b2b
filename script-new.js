// ===== SIMPLE AND RELIABLE JAVASCRIPT =====
console.log('🚀 Loading new script...');

// Global variables
let activeSection = 'home';
let selectedService = '';

// Wait for page to load completely
window.addEventListener('load', function() {
    console.log('✅ Page fully loaded, initializing...');
    
    // Initialize everything
    setupButtonHandlers();
    setupScrollEffects();
    setupMobileMenu();
    setupFloatingButtons();
    
    console.log('✅ All systems ready!');
});

// Setup button handlers
function setupButtonHandlers() {
    console.log('🔧 Setting up button handlers...');
    
    // Handle all clicks on the document
    document.body.addEventListener('click', function(event) {
        console.log('👆 Click detected on:', event.target.tagName);
        
        // Find the clicked button or link
        let target = event.target;
        
        // Look for button or link up the DOM tree
        while (target && target !== document.body) {
            if (target.tagName === 'BUTTON' || target.tagName === 'A') {
                handleButtonClick(target, event);
                break;
            }
            target = target.parentElement;
        }
    });
    
    console.log('✅ Button handlers setup complete');
}

// Handle button clicks
function handleButtonClick(button, event) {
    console.log('🎯 Button clicked:', button.textContent.trim());
    
    // Check for data-action attribute
    const action = button.getAttribute('data-action');
    if (action) {
        event.preventDefault();
        console.log('📋 Action found:', action);
        
        if (action === 'scroll') {
            const target = button.getAttribute('data-target');
            if (target) {
                console.log('📍 Scrolling to:', target);
                scrollToSection(target);
            }
        }
        
        if (action === 'select-service') {
            const service = button.getAttribute('data-service');
            if (service) {
                console.log('🛠️ Selecting service:', service);
                selectService(service);
            }
        }
        return;
    }
    
    // Check for onclick attribute (fallback)
    const onclick = button.getAttribute('onclick');
    if (onclick) {
        event.preventDefault();
        console.log('📋 Onclick found:', onclick);
        
        // Handle scrollToSection
        if (onclick.includes('scrollToSection')) {
            const match = onclick.match(/scrollToSection\('([^']+)'\)/);
            if (match) {
                console.log('📍 Scrolling to:', match[1]);
                scrollToSection(match[1]);
            }
        }
        
        // Handle selectService
        if (onclick.includes('selectService')) {
            const match = onclick.match(/selectService\('([^']+)'\)/);
            if (match) {
                console.log('🛠️ Selecting service:', match[1]);
                selectService(match[1]);
            }
        }
        
        // Handle toggleMobileMenu
        if (onclick.includes('toggleMobileMenu')) {
            console.log('📱 Toggling mobile menu');
            toggleMobileMenu();
        }
        
        // Handle form submission
        if (onclick.includes('handleContactSubmit')) {
            const form = button.closest('form');
            if (form) {
                console.log('📧 Submitting form');
                handleContactSubmit({ target: form, preventDefault: () => {} });
            }
        }
    }
}

// Scroll to section function
function scrollToSection(sectionId) {
    console.log('🎯 scrollToSection called with:', sectionId);
    
    const element = document.getElementById(sectionId);
    if (!element) {
        console.error('❌ Section not found:', sectionId);
        return;
    }
    
    const headerHeight = 70;
    const elementPosition = element.offsetTop - headerHeight;
    
    // Scroll to the element
    window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
    });
    
    activeSection = sectionId;
    updateActiveNavLink();
    
    // Close mobile menu
    const mobileNav = document.getElementById('mobileNav');
    if (mobileNav && mobileNav.classList.contains('active')) {
        mobileNav.classList.remove('active');
        updateMobileMenuIcon();
    }
    
    console.log('✅ Scrolled to:', sectionId);
}

// Select service function
function selectService(serviceName) {
    console.log('🎯 selectService called with:', serviceName);
    
    selectedService = serviceName;
    
    // Scroll to contact section
    scrollToSection('contact');
    
    // Update the service dropdown
    setTimeout(() => {
        const serviceSelect = document.getElementById('selectedService');
        if (serviceSelect) {
            serviceSelect.value = serviceName;
            console.log('✅ Service dropdown updated');
        }
        
        // Show notification
        showNotification(`تم اختيار خدمة: ${serviceName}`, 'success');
    }, 500);
    
    console.log('✅ Service selected:', serviceName);
}

// Show notification
function showNotification(message, type = 'info') {
    console.log('📢 Showing notification:', message);
    
    // Remove existing notifications
    const existing = document.querySelectorAll('.notification');
    existing.forEach(n => n.remove());
    
    // Create notification
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 90px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : '#3b82f6'};
        color: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 1001;
        max-width: 300px;
        animation: slideIn 0.3s ease-out;
    `;
    
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" 
                    style="background: none; border: none; color: white; cursor: pointer; margin-left: auto;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Mobile menu functions
function toggleMobileMenu() {
    const mobileNav = document.getElementById('mobileNav');
    if (mobileNav) {
        mobileNav.classList.toggle('active');
        updateMobileMenuIcon();
    }
}

function updateMobileMenuIcon() {
    const menuBtn = document.querySelector('.mobile-menu-btn i');
    const mobileNav = document.getElementById('mobileNav');
    if (mobileNav && menuBtn) {
        if (mobileNav.classList.contains('active')) {
            menuBtn.className = 'fas fa-times';
        } else {
            menuBtn.className = 'fas fa-bars';
        }
    }
}

// Update active navigation link
function updateActiveNavLink() {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
        const href = link.getAttribute('href');
        if (href && href.substring(1) === activeSection) {
            link.classList.add('active');
        }
    });
}

// Setup scroll effects
function setupScrollEffects() {
    window.addEventListener('scroll', function() {
        updateHeaderBackground();
        updateActiveSection();
    });
}

function updateHeaderBackground() {
    const header = document.getElementById('header');
    if (header) {
        if (window.scrollY > 50) {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.8)';
            header.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.05)';
        }
    }
}

function updateActiveSection() {
    const sections = ['home', 'services', 'portfolio', 'contact'];
    const scrollPosition = window.scrollY + 100;

    for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
            const { offsetTop, offsetHeight } = element;
            if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
                if (activeSection !== section) {
                    activeSection = section;
                    updateActiveNavLink();
                }
                break;
            }
        }
    }
}

// Setup mobile menu
function setupMobileMenu() {
    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        const mobileNav = document.getElementById('mobileNav');
        if (mobileNav) {
            const isClickInsideNav = mobileNav.contains(event.target);
            const isClickOnMenuBtn = event.target.closest('.mobile-menu-btn');
            
            if (!isClickInsideNav && !isClickOnMenuBtn && mobileNav.classList.contains('active')) {
                mobileNav.classList.remove('active');
                updateMobileMenuIcon();
            }
        }
    });
}

// Handle contact form submission
function handleContactSubmit(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData);
    
    // Send WhatsApp message
    sendWhatsAppMessage(data);
    
    showNotification('تم إرسال رسالتك بنجاح! سيتم توجيهك للواتساب الآن.', 'success');
    
    // Reset form
    event.target.reset();
    
    console.log('Form submitted:', data);
}

// Send WhatsApp message
function sendWhatsAppMessage(formData) {
    const phoneNumber = '212782297888';
    
    const message = `🌟 طلب جديد من موقع Baroua Code 🌟

👤 الاسم: ${formData.name || 'غير محدد'}
📧 البريد الإلكتروني: ${formData.email || 'غير محدد'}
📱 الهاتف: ${formData.phone || 'غير محدد'}
🛠️ الخدمة المطلوبة: ${formData.service || 'غير محددة'}

📝 تفاصيل المشروع:
${formData.message || 'لا توجد تفاصيل إضافية'}

⏰ تاريخ الطلب: ${new Date().toLocaleString('ar-SA')}

نتطلع للعمل معك! 🚀`;
    
    const encodedMessage = encodeURIComponent(message);
    const whatsappURL = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
    
    setTimeout(() => {
        window.open(whatsappURL, '_blank');
    }, 1000);
}

// Setup floating buttons
function setupFloatingButtons() {
    // Add floating WhatsApp button
    const whatsappBtn = document.createElement('a');
    whatsappBtn.href = `https://wa.me/212782297888?text=${encodeURIComponent('مرحباً! أنا مهتم بخدمات تطوير المواقع')}`;
    whatsappBtn.target = '_blank';
    whatsappBtn.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 20px;
        background: #25d366;
        color: white;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        text-decoration: none;
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
        z-index: 1000;
        transition: all 0.3s ease;
    `;
    whatsappBtn.innerHTML = '<i class="fab fa-whatsapp"></i>';
    
    whatsappBtn.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.1)';
    });
    
    whatsappBtn.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
    });
    
    document.body.appendChild(whatsappBtn);
}

console.log('✅ New script loaded successfully');
