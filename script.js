// Global variables
let activeSection = 'home';
let selectedService = '';

// DOM Elements
const header = document.getElementById('header');
const mobileNav = document.getElementById('mobileNav');
const navLinks = document.querySelectorAll('.nav-link');

// Initialize the website
document.addEventListener('DOMContentLoaded', function() {
    initializeScrollEffects();
    initializeAnimations();
    updateActiveNavLink();
});

// Smooth scrolling to sections
function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        const headerHeight = 70;
        const elementPosition = element.offsetTop - headerHeight;
        
        window.scrollTo({
            top: elementPosition,
            behavior: 'smooth'
        });
        
        activeSection = sectionId;
        updateActiveNavLink();
        
        // Close mobile menu if open
        mobileNav.classList.remove('active');
        updateMobileMenuIcon();
    }
}

// Toggle mobile menu
function toggleMobileMenu() {
    mobileNav.classList.toggle('active');
    updateMobileMenuIcon();
}

// Update mobile menu icon
function updateMobileMenuIcon() {
    const menuBtn = document.querySelector('.mobile-menu-btn i');
    if (mobileNav.classList.contains('active')) {
        menuBtn.className = 'fas fa-times';
    } else {
        menuBtn.className = 'fas fa-bars';
    }
}

// Update active navigation link
function updateActiveNavLink() {
    navLinks.forEach(link => {
        link.classList.remove('active');
        const href = link.getAttribute('href').substring(1);
        if (href === activeSection) {
            link.classList.add('active');
        }
    });
}

// Handle service selection
function selectService(serviceName) {
    selectedService = serviceName;
    scrollToSection('contact');
    
    // Show selected service in contact form (if contact section exists)
    setTimeout(() => {
        const serviceInput = document.getElementById('selectedService');
        if (serviceInput) {
            serviceInput.value = serviceName;
        }
        
        // Show notification
        showNotification(`تم اختيار خدمة: ${serviceName}`, 'success');
    }, 500);
}

// Show notification
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    // Add styles for notification
    notification.style.cssText = `
        position: fixed;
        top: 90px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : '#3b82f6'};
        color: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 1001;
        animation: slideInRight 0.3s ease-out;
        max-width: 300px;
    `;
    
    // Add notification to body
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => notification.remove(), 300);
        }
    }, 5000);
}

// Initialize scroll effects
function initializeScrollEffects() {
    window.addEventListener('scroll', function() {
        updateHeaderBackground();
        updateActiveSection();
        animateOnScroll();
    });
}

// Update header background on scroll
function updateHeaderBackground() {
    if (window.scrollY > 50) {
        header.style.background = 'rgba(255, 255, 255, 0.95)';
        header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
    } else {
        header.style.background = 'rgba(255, 255, 255, 0.8)';
        header.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.05)';
    }
}

// Update active section based on scroll position
function updateActiveSection() {
    const sections = ['home', 'services', 'portfolio', 'contact'];
    const scrollPosition = window.scrollY + 100;

    for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
            const { offsetTop, offsetHeight } = element;
            if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
                if (activeSection !== section) {
                    activeSection = section;
                    updateActiveNavLink();
                }
                break;
            }
        }
    }
}

// Animate elements on scroll
function animateOnScroll() {
    const elements = document.querySelectorAll('.service-card, .why-card');
    
    elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;
        
        if (elementTop < window.innerHeight - elementVisible) {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }
    });
}

// Initialize animations
function initializeAnimations() {
    // Add CSS for animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
        
        .notification-content {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .notification-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            margin-right: auto;
            padding: 0.25rem;
            border-radius: 50%;
            transition: background 0.3s ease;
        }
        
        .notification-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .service-card, .why-card {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
    `;
    document.head.appendChild(style);
    
    // Trigger initial animation check
    setTimeout(animateOnScroll, 100);
}

// Handle form submission (if contact form exists)
function handleContactSubmit(event) {
    event.preventDefault();
    
    // Get form data
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData);
    
    // Simulate form submission
    showNotification('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.', 'success');
    
    // Reset form
    event.target.reset();
    
    // Log data for development (remove in production)
    console.log('Form submitted:', data);
}

// Utility function to create WhatsApp link
function createWhatsAppLink(message = 'مرحباً! أنا مهتم بخدمات تطوير المواقع') {
    const phoneNumber = '+1234567890'; // Replace with actual phone number
    const encodedMessage = encodeURIComponent(message);
    return `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
}

// Add floating WhatsApp button
function addFloatingWhatsApp() {
    const whatsappBtn = document.createElement('a');
    whatsappBtn.href = createWhatsAppLink();
    whatsappBtn.target = '_blank';
    whatsappBtn.rel = 'noopener noreferrer';
    whatsappBtn.className = 'floating-whatsapp';
    whatsappBtn.innerHTML = `
        <i class="fab fa-whatsapp"></i>
        <div class="tooltip">تحدث معنا!</div>
    `;
    
    // Add styles
    whatsappBtn.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 20px;
        background: #25d366;
        color: white;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        text-decoration: none;
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
        z-index: 1000;
        transition: all 0.3s ease;
    `;
    
    // Add hover effects
    whatsappBtn.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.1)';
        this.style.boxShadow = '0 6px 25px rgba(37, 211, 102, 0.4)';
    });
    
    whatsappBtn.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
        this.style.boxShadow = '0 4px 20px rgba(37, 211, 102, 0.3)';
    });
    
    document.body.appendChild(whatsappBtn);
}

// Add scroll to top button
function addScrollToTopButton() {
    const scrollBtn = document.createElement('button');
    scrollBtn.className = 'scroll-to-top';
    scrollBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    scrollBtn.onclick = () => scrollToSection('home');
    
    scrollBtn.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #3b82f6;
        color: white;
        width: 50px;
        height: 50px;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
        z-index: 1000;
        transition: all 0.3s ease;
        opacity: 0;
        transform: translateY(20px);
    `;
    
    // Show/hide based on scroll position
    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            scrollBtn.style.opacity = '1';
            scrollBtn.style.transform = 'translateY(0)';
        } else {
            scrollBtn.style.opacity = '0';
            scrollBtn.style.transform = 'translateY(20px)';
        }
    });
    
    // Add hover effects
    scrollBtn.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px) scale(1.05)';
        this.style.boxShadow = '0 6px 25px rgba(59, 130, 246, 0.4)';
    });
    
    scrollBtn.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = '0 4px 20px rgba(59, 130, 246, 0.3)';
    });
    
    document.body.appendChild(scrollBtn);
}

// Initialize floating buttons when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    addFloatingWhatsApp();
    addScrollToTopButton();
});

// Close mobile menu when clicking outside
document.addEventListener('click', function(event) {
    const isClickInsideNav = mobileNav.contains(event.target);
    const isClickOnMenuBtn = event.target.closest('.mobile-menu-btn');
    
    if (!isClickInsideNav && !isClickOnMenuBtn && mobileNav.classList.contains('active')) {
        mobileNav.classList.remove('active');
        updateMobileMenuIcon();
    }
});
