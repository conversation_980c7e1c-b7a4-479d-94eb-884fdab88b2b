<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط للأزرار</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            line-height: 1.6;
        }
        
        .btn {
            padding: 1rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            font-size: 1rem;
            margin: 10px;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }
        
        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
        
        .section {
            height: 500px;
            background: #f3f4f6;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #e5e7eb;
        }
        
        .log {
            background: #1f2937;
            color: #10b981;
            padding: 15px;
            margin: 20px 0;
            border-radius: 10px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            font-size: 14px;
        }
        
        .status {
            background: #065f46;
            color: #d1fae5;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار الأزرار - نسخة مبسطة</h1>
    
    <div class="status" id="status">
        ⏳ جاري التحميل...
    </div>
    
    <div>
        <h2>الأزرار للاختبار:</h2>
        
        <button class="btn btn-primary" data-action="scroll" data-target="services">
            📍 انتقل للخدمات
        </button>
        
        <button class="btn btn-primary" data-action="scroll" data-target="contact">
            📞 انتقل للتواصل
        </button>
        
        <button class="btn btn-primary" data-action="select-service" data-service="تطوير المواقع">
            🛠️ اختر تطوير المواقع
        </button>
        
        <button class="btn btn-primary" data-action="select-service" data-service="فحص الأمان">
            🔒 اختر فحص الأمان
        </button>
    </div>
    
    <div class="log" id="log">
        <strong>📋 سجل الأحداث:</strong><br>
    </div>
    
    <div class="section" id="services">
        <h2>🛠️ قسم الخدمات</h2>
        <p>هذا هو قسم الخدمات للاختبار</p>
    </div>
    
    <div class="section" id="contact">
        <h2>📞 قسم التواصل</h2>
        <p>هذا هو قسم التواصل للاختبار</p>
    </div>

    <script>
        let clickCount = 0;
        
        // Override console.log to show in page
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${args.join(' ')}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        };

        // Test functions
        function scrollToSection(sectionId) {
            console.log(`🎯 scrollToSection("${sectionId}") called`);
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
                console.log(`✅ Successfully scrolled to ${sectionId}`);
                return true;
            } else {
                console.log(`❌ Element "${sectionId}" not found`);
                return false;
            }
        }

        function selectService(serviceName) {
            console.log(`🎯 selectService("${serviceName}") called`);
            console.log(`✅ Service "${serviceName}" selected successfully`);
            
            // Simulate scrolling to contact
            setTimeout(() => {
                console.log(`📞 Auto-scrolling to contact section...`);
                scrollToSection('contact');
            }, 500);
            
            return true;
        }

        // Setup event listeners
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM Content Loaded');
            
            // Update status
            document.getElementById('status').innerHTML = '✅ تم التحميل بنجاح - جاهز للاختبار';
            
            // Main click handler
            document.addEventListener('click', function(event) {
                clickCount++;
                console.log(`🖱️ Click #${clickCount} detected`);
                
                const button = event.target.closest('[data-action]');
                if (!button) {
                    console.log('ℹ️ Click was not on a button with data-action');
                    return;
                }
                
                const action = button.getAttribute('data-action');
                console.log(`🔄 Button action: "${action}"`);
                
                if (action === 'scroll') {
                    event.preventDefault();
                    const target = button.getAttribute('data-target');
                    if (target) {
                        console.log(`📍 Scroll target: "${target}"`);
                        scrollToSection(target);
                    } else {
                        console.log('❌ No target specified for scroll action');
                    }
                }
                
                if (action === 'select-service') {
                    event.preventDefault();
                    const service = button.getAttribute('data-service');
                    if (service) {
                        console.log(`🛠️ Service to select: "${service}"`);
                        selectService(service);
                    } else {
                        console.log('❌ No service specified for select-service action');
                    }
                }
            });
            
            console.log('✅ Event listeners setup complete');
            console.log('🎮 Ready for testing! Click any button above.');
        });
    </script>
</body>
</html>
